{"rustc": 1842507548689473721, "features": "[\"ahash\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 2487185053407325305, "deps": [[966925859616469517, "ahash", false, 18104927564911044552]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-528396ca9cbbd6f3\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}