{"rustc": 1842507548689473721, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 14162657976132989036, "profile": 2241668132362809309, "path": 15706262531478369273, "deps": [[16675652872862304210, "build_script_build", false, 7619689475658642097]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-ecef1607d626f318\\dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}