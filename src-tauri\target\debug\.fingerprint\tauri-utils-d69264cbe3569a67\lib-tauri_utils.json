{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 15645168533844550301], [1200537532907108615, "url<PERSON><PERSON>n", false, 5747881295922131212], [3060637413840920116, "proc_macro2", false, 5373005460213438867], [3129130049864710036, "memchr", false, 2476762750074238256], [3150220818285335163, "url", false, 9322067634279066546], [3191507132440681679, "serde_untagged", false, 17227096661675603816], [4899080583175475170, "semver", false, 18089378944928502309], [5986029879202738730, "log", false, 16544253528419911208], [6213549728662707793, "serde_with", false, 10684210762440900404], [6262254372177975231, "kuchiki", false, 4338501209115019347], [6606131838865521726, "ctor", false, 14477600700006865172], [6913375703034175521, "schemars", false, 7790464681362503323], [7170110829644101142, "json_patch", false, 6373899631276182243], [8319709847752024821, "uuid", false, 3277262117305138326], [9010263965687315507, "http", false, 11824410263065750636], [9451456094439810778, "regex", false, 17554651505823345870], [9689903380558560274, "serde", false, 8619023586440547223], [10806645703491011684, "thiserror", false, 4998919084893543262], [11655476559277113544, "cargo_metadata", false, 14986228484535667864], [11989259058781683633, "dunce", false, 9698422839952381541], [13625485746686963219, "anyhow", false, 11841672266104157151], [14132538657330703225, "brotli", false, 951849290397099808], [15367738274754116744, "serde_json", false, 7771385405482084678], [15609422047640926750, "toml", false, 13392820443170264866], [15622660310229662834, "walkdir", false, 18042159149950293803], [17146114186171651583, "infer", false, 5475706784718395423], [17155886227862585100, "glob", false, 5597488296261464133], [17186037756130803222, "phf", false, 7820529993604048686], [17990358020177143287, "quote", false, 9227116837396871820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-d69264cbe3569a67\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}