{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12844171384082609526, "deps": [[4899080583175475170, "semver", false, 18089378944928502309], [6913375703034175521, "schemars", false, 7790464681362503323], [7170110829644101142, "json_patch", false, 6373899631276182243], [9689903380558560274, "serde", false, 8619023586440547223], [11050281405049894993, "tauri_utils", false, 983778964914959900], [12714016054753183456, "tauri_winres", false, 9118340029872331921], [13077543566650298139, "heck", false, 8749295945489699306], [13475171727366188400, "cargo_toml", false, 8977461706422143683], [13625485746686963219, "anyhow", false, 11841672266104157151], [15367738274754116744, "serde_json", false, 7771385405482084678], [15609422047640926750, "toml", false, 13392820443170264866], [15622660310229662834, "walkdir", false, 18042159149950293803], [16928111194414003569, "dirs", false, 2510716140661615147], [17155886227862585100, "glob", false, 5597488296261464133]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-34f802c7b169ea05\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}