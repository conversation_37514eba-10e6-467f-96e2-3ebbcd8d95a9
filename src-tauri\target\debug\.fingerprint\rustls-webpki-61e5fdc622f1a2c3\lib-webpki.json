{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2241668132362809309, "path": 12590007092919164603, "deps": [[2883436298747778685, "pki_types", false, 1035697240254743143], [5491919304041016563, "ring", false, 1427705113378174692], [8995469080876806959, "untrusted", false, 522613454715898202]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-61e5fdc622f1a2c3\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}