{"rustc": 1842507548689473721, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2241668132362809309, "path": 13718170166304641978, "deps": [[3056352129074654578, "hashlink", false, 5035375939227404736], [3666196340704888985, "smallvec", false, 2239747225848218171], [5510864063823219921, "fallible_streaming_iterator", false, 15007973858404644926], [7896293946984509699, "bitflags", false, 2254834069894658794], [12860549049674006569, "fallible_iterator", false, 6618675631106217660], [16675652872862304210, "libsqlite3_sys", false, 7863633126507076818]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-735530f5c06fddd4\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}