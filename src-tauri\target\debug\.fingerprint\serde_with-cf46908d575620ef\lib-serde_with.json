{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 511476171623209585, "path": 11805383375341948447, "deps": [[6158493786865284961, "serde_with_macros", false, 10447158253201468767], [9689903380558560274, "serde", false, 1448236826619105632], [16257276029081467297, "serde_derive", false, 5363810503285475230]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-cf46908d575620ef\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}