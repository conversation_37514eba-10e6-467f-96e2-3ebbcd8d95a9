{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 2098866745747931283], [10755362358622467486, "build_script_build", false, 5623007392882674688], [13919194856117907555, "build_script_build", false, 12959305562626490844], [3834743577069889284, "build_script_build", false, 17036322666277951344], [13890802266741835355, "build_script_build", false, 8891004220602881802], [246920333930397414, "build_script_build", false, 5545055751797369063], [15441187897486245138, "build_script_build", false, 5484503010079270386], [7849236192756901113, "build_script_build", false, 451353842279490375], [17962022290347926134, "build_script_build", false, 9206837256607856812], [1582828171158827377, "build_script_build", false, 6248125940949304321], [18440762029541581206, "build_script_build", false, 15374924264285477504]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-9911e3706fb7e188\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}