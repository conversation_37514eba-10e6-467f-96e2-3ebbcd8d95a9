{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 9958066978812107341, "deps": [[784494742817713399, "tower_service", false, 891412978182292050], [1906322745568073236, "pin_project_lite", false, 27178801118851068], [4121350475192885151, "iri_string", false, 4419605220616355901], [5695049318159433696, "tower", false, 809013410114240084], [7712452662827335977, "tower_layer", false, 14062717672081574200], [7896293946984509699, "bitflags", false, 2254834069894658794], [9010263965687315507, "http", false, 4349961928876268954], [10629569228670356391, "futures_util", false, 3598648167291060936], [14084095096285906100, "http_body", false, 14653673679686981800], [16066129441945555748, "bytes", false, 14757824830115038577]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-2f7fdac1c92a3013\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}