{"rustc": 1842507548689473721, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 829593805026033690, "deps": [[3214373357989284387, "pkg_config", false, 13252006675240338182], [3378925969027653845, "cc", false, 3359284602402674138], [12933202132622624734, "vcpkg", false, 9653792424346614541]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-8275f1a38c3df0ea\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}