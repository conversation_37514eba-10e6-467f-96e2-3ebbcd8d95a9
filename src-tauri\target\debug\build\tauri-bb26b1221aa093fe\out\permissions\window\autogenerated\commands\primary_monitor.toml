# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-primary-monitor"
description = "Enables the primary_monitor command without any pre-configured scope."
commands.allow = ["primary_monitor"]

[[permission]]
identifier = "deny-primary-monitor"
description = "Denies the primary_monitor command without any pre-configured scope."
commands.deny = ["primary_monitor"]
