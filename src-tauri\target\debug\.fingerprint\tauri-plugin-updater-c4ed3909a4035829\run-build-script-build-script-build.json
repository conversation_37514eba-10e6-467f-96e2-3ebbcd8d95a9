{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 5623007392882674688], [18440762029541581206, "build_script_build", false, 3538939924574190814]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-c4ed3909a4035829\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}