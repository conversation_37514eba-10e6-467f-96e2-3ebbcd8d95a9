{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8985965904563823836, "deps": [[3060637413840920116, "proc_macro2", false, 5373005460213438867], [3150220818285335163, "url", false, 9322067634279066546], [4899080583175475170, "semver", false, 18089378944928502309], [7170110829644101142, "json_patch", false, 6373899631276182243], [7392050791754369441, "ico", false, 11522824965247104541], [8319709847752024821, "uuid", false, 3277262117305138326], [9689903380558560274, "serde", false, 8619023586440547223], [9857275760291862238, "sha2", false, 12192013078506410478], [10806645703491011684, "thiserror", false, 4998919084893543262], [11050281405049894993, "tauri_utils", false, 983778964914959900], [12687914511023397207, "png", false, 3002059728021278096], [13077212702700853852, "base64", false, 1184606693524608866], [14132538657330703225, "brotli", false, 951849290397099808], [15367738274754116744, "serde_json", false, 7771385405482084678], [15622660310229662834, "walkdir", false, 18042159149950293803], [17990358020177143287, "quote", false, 9227116837396871820], [18149961000318489080, "syn", false, 14812274515719501434]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-8b8515fb9ad84812\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}