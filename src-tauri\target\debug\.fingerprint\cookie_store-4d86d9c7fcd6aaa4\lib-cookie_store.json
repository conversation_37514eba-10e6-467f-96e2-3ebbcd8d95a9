{"rustc": 1842507548689473721, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 2241668132362809309, "path": 17266973371716140037, "deps": [[505596520502798227, "publicsuffix", false, 9716173063419878717], [3150220818285335163, "url", false, 941023336966268525], [5986029879202738730, "log", false, 15167994853724410088], [6376232718484714452, "idna", false, 7443585763699583151], [9689903380558560274, "serde", false, 1448236826619105632], [11763018104473073732, "document_features", false, 13217038338996003124], [12409575957772518135, "time", false, 18053917805389135447], [15367738274754116744, "serde_json", false, 8761122664160837064], [16257276029081467297, "serde_derive", false, 5363810503285475230], [16727543399706004146, "cookie", false, 6692455882241929381]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie_store-4d86d9c7fcd6aaa4\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}