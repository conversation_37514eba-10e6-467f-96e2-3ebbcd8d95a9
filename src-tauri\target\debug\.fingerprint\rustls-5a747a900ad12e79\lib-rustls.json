{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5788819337146887687, "path": 1749457146276060112, "deps": [[2883436298747778685, "pki_types", false, 1035697240254743143], [3722963349756955755, "once_cell", false, 13244492203321078007], [5491919304041016563, "ring", false, 1427705113378174692], [6528079939221783635, "zeroize", false, 1365563214137681776], [16400140949089969347, "build_script_build", false, 17963391567832002887], [17003143334332120809, "subtle", false, 17106190257001807791], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 12904970958857848505]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-5a747a900ad12e79\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}