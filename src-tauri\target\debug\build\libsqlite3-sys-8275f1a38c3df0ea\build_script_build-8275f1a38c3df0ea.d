C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\libsqlite3-sys-8275f1a38c3df0ea\build_script_build-8275f1a38c3df0ea.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

C:\Users\<USER>\Desktop\claudia\src-tauri\target\debug\build\libsqlite3-sys-8275f1a38c3df0ea\build_script_build-8275f1a38c3df0ea.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs:

# env-dep:CARGO_MANIFEST_DIR=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libsqlite3-sys-0.30.1
