{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 5623007392882674688], [13890802266741835355, "build_script_build", false, 8891004220602881802], [15441187897486245138, "build_script_build", false, 5882518838500500820]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-4d46b72203db5481\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}